import { useState } from 'react'
import SearchBar from './components/SearchBar.jsx'
import Weather from './components/WeatherCards.jsx'
import Axios from 'axios';
import './App.css'

function App() {
  const [catFact, setCatFact] = useState('')
  Axios.get('https://openweathermap.org/api').then((res)=>{
    setCatFact(res.data.fact)
  })
  

  return (
    <div className='App'>
      <p>{catFact}</p>

    </div>

  )
}

export default App
