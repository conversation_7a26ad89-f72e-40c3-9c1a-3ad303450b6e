



function WeatherCards({date}) {


    if (!date) return null;``

    const {name,main,weather}=date;
    const iconUrl=`http://openweathermap.org/img/wn/${weather[0].icon}@2x.png`;
    return(
        <div className="card"> 
        <h1 className="city-card">{name}</h1>
        <img src={iconUrl} alt={weather[0].description} />
        <p className="temp-card">{Math.round(main.temp)}°C</p>
        <p className="capitalize-card">{weather[0].description}</p>
        <p>Humidity: {main.humidity}%</p>
        <p>Pressure: {main.pressure} hPa</p>



        </div>
    );
};

export default WeatherCards;