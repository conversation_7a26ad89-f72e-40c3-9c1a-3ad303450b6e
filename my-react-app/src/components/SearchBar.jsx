

import { useState } from "react";


function SearchBar() {
    const [City,setCity]=useState('');

    const handleSubmit=(e)=>{
        e.preventDefault();
        if (City) onSearch(City);
        setCity('');
    };





    return(
        <form onSubmit={handleSubmit} className="mb-4">
            <input 
            type='text'
            className="form-control"
            placeholder='Search city'
            value=' City'
            onChange={(e)=>setCity(e.target.value)}
            />
            <button type="submit" className="btn btn-primary">Search</button>

        </form>

    );
};

export default SearchBar;